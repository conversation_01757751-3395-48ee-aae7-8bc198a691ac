* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.app-container {
    display: flex;
    height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: white;
    padding: 20px 0;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.logo {
    text-align: center;
    padding: 20px;
    border-bottom: 1px solid #34495e;
    margin-bottom: 20px;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
    color: #ecf0f1;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin: 5px 0;
}

.nav-link {
    display: block;
    padding: 15px 25px;
    color: #ecf0f1;
    text-decoration: none;
    transition: background-color 0.3s;
    border-radius: 0 25px 25px 0;
    margin-right: 10px;
}

.nav-link:hover,
.nav-link.active {
    background-color: #3498db;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #fff;
}

.page-header {
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ecf0f1;
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
}

/* 通用样式 */
.loading, .welcome-message {
    text-align: center;
    margin-top: 100px;
}

.loading h2, .welcome-message h2 {
    color: #3498db;
    margin-bottom: 20px;
    font-size: 24px;
}

.loading p, .welcome-message p {
    color: #7f8c8d;
    font-size: 16px;
    margin-bottom: 10px;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e1e8ed;
}

.card-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ecf0f1;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #229954;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-input, .form-select, .form-textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.table tr:hover {
    background-color: #f8f9fa;
}

/* 网格布局 */
.grid {
    display: grid;
    gap: 20px;
}

.grid-2 {
    grid-template-columns: 1fr 1fr;
}

.grid-3 {
    grid-template-columns: 1fr 1fr 1fr;
}

.grid-4 {
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

/* KPI卡片 */
.kpi-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.kpi-value {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
}

.kpi-label {
    font-size: 14px;
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .nav-menu {
        display: flex;
        justify-content: space-around;
    }
    
    .nav-link {
        border-radius: 5px;
        margin: 0 5px;
    }
    
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }
}
