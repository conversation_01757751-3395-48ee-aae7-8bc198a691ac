# 安装与部署指南

本指南将帮助您在本地环境中安装和运行XAUUSD交易日志与分析系统。

## 📋 系统要求

### 最低要求
- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Python**: 3.6+ (用于本地开发服务器)
- **存储空间**: 至少50MB可用空间

### 推荐配置
- **内存**: 4GB RAM或更多
- **处理器**: 双核2.0GHz或更高
- **网络**: 仅用于下载依赖，运行时无需网络连接
- **屏幕分辨率**: 1366×768或更高

## 🚀 快速安装

### 方法1: 直接下载（推荐）

1. **下载项目文件**
   ```bash
   # 如果有Git
   git clone https://github.com/your-username/xauusd-trading-journal.git
   cd xauusd-trading-journal
   
   # 或者直接下载ZIP文件并解压
   ```

2. **启动应用**
   ```bash
   # 使用Python启动本地服务器
   python3 -m http.server 8080
   
   # 或者使用Python 2
   python -m SimpleHTTPServer 8080
   ```

3. **打开浏览器**
   访问: http://localhost:8080

### 方法2: 使用Node.js

1. **安装Node.js**
   - 访问 [Node.js官网](https://nodejs.org/) 下载并安装

2. **安装依赖**
   ```bash
   cd xauusd-trading-journal
   npm install
   ```

3. **启动应用**
   ```bash
   npm start
   ```

4. **打开浏览器**
   访问: http://localhost:8080

## 🔧 详细安装步骤

### Windows系统

#### 使用Python
1. **安装Python**
   - 访问 [Python官网](https://www.python.org/downloads/) 下载Python 3.x
   - 安装时勾选"Add Python to PATH"

2. **验证安装**
   ```cmd
   python --version
   ```

3. **下载项目**
   ```cmd
   # 下载并解压项目文件到 C:\xauusd-trading-journal
   cd C:\xauusd-trading-journal
   ```

4. **启动服务器**
   ```cmd
   python -m http.server 8080
   ```

5. **访问应用**
   打开浏览器访问: http://localhost:8080

#### 使用XAMPP（可选）
1. **下载XAMPP**
   - 访问 [XAMPP官网](https://www.apachefriends.org/) 下载安装

2. **配置项目**
   - 将项目文件复制到 `C:\xampp\htdocs\xauusd-trading-journal`
   - 启动Apache服务

3. **访问应用**
   打开浏览器访问: http://localhost/xauusd-trading-journal

### macOS系统

#### 使用Python（预装）
1. **验证Python**
   ```bash
   python3 --version
   ```

2. **下载项目**
   ```bash
   cd ~/Downloads
   # 下载并解压项目文件
   cd xauusd-trading-journal
   ```

3. **启动服务器**
   ```bash
   python3 -m http.server 8080
   ```

4. **访问应用**
   打开浏览器访问: http://localhost:8080

#### 使用Homebrew + Node.js
1. **安装Homebrew**
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. **安装Node.js**
   ```bash
   brew install node
   ```

3. **启动项目**
   ```bash
   cd xauusd-trading-journal
   npm install
   npm start
   ```

### Linux系统

#### Ubuntu/Debian
1. **更新包管理器**
   ```bash
   sudo apt update
   ```

2. **安装Python**
   ```bash
   sudo apt install python3 python3-pip
   ```

3. **下载项目**
   ```bash
   cd ~/Downloads
   # 下载并解压项目文件
   cd xauusd-trading-journal
   ```

4. **启动服务器**
   ```bash
   python3 -m http.server 8080
   ```

#### CentOS/RHEL
1. **安装Python**
   ```bash
   sudo yum install python3 python3-pip
   # 或者使用dnf (较新版本)
   sudo dnf install python3 python3-pip
   ```

2. **启动项目**
   ```bash
   cd xauusd-trading-journal
   python3 -m http.server 8080
   ```

## 🌐 部署选项

### 本地部署（推荐）
- **优点**: 数据完全本地化，隐私安全
- **缺点**: 只能在本机访问
- **适用**: 个人使用，数据敏感场景

### 局域网部署
1. **启动服务器**
   ```bash
   # 绑定到所有网络接口
   python3 -m http.server 8080 --bind 0.0.0.0
   ```

2. **获取本机IP**
   ```bash
   # Windows
   ipconfig
   
   # macOS/Linux
   ifconfig
   ```

3. **局域网访问**
   其他设备访问: http://[本机IP]:8080

### 云服务器部署
1. **选择云服务商**
   - 阿里云、腾讯云、AWS等

2. **配置服务器**
   ```bash
   # 安装Nginx
   sudo apt install nginx
   
   # 配置静态文件服务
   sudo nano /etc/nginx/sites-available/xauusd-trading-journal
   ```

3. **Nginx配置示例**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           root /var/www/xauusd-trading-journal;
           index index.html;
           try_files $uri $uri/ /index.html;
       }
   }
   ```

## 🔒 安全配置

### HTTPS配置（生产环境）
1. **获取SSL证书**
   ```bash
   # 使用Let's Encrypt
   sudo apt install certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

2. **自动续期**
   ```bash
   sudo crontab -e
   # 添加: 0 12 * * * /usr/bin/certbot renew --quiet
   ```

### 访问控制
1. **IP白名单**
   ```nginx
   location / {
       allow ***********/24;
       deny all;
       # 其他配置...
   }
   ```

2. **基础认证**
   ```nginx
   location / {
       auth_basic "Trading Journal";
       auth_basic_user_file /etc/nginx/.htpasswd;
       # 其他配置...
   }
   ```

## 🛠️ 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :8080

# 使用其他端口
python3 -m http.server 8081
```

#### 2. Python命令不存在
```bash
# 尝试不同的Python命令
python --version
python3 --version
py --version  # Windows
```

#### 3. 浏览器缓存问题
- 按 Ctrl+F5 强制刷新
- 清除浏览器缓存
- 使用无痕模式测试

#### 4. 文件权限问题（Linux/macOS）
```bash
# 修改文件权限
chmod -R 755 xauusd-trading-journal
```

#### 5. 防火墙阻止访问
```bash
# Ubuntu/Debian
sudo ufw allow 8080

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

### 性能优化

#### 1. 启用Gzip压缩（Nginx）
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/json;
```

#### 2. 设置缓存头
```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

#### 3. 启用HTTP/2
```nginx
listen 443 ssl http2;
```

## 📱 移动端访问

### 响应式设计
- 应用已优化移动端显示
- 支持触摸操作
- 自适应屏幕尺寸

### PWA功能（计划中）
- 离线访问支持
- 添加到主屏幕
- 推送通知

## 🔄 更新升级

### 手动更新
1. **备份数据**
   - 使用应用内导出功能
   - 备份localStorage数据

2. **下载新版本**
   - 替换应用文件
   - 保留数据文件

3. **恢复数据**
   - 使用导入功能恢复数据

### 自动更新（计划中）
- 版本检查机制
- 增量更新支持
- 数据迁移自动化

## 📞 技术支持

### 获取帮助
- **文档**: 查看README.md和技术文档
- **问题反馈**: 提交GitHub Issues
- **社区讨论**: 参与项目讨论

### 联系方式
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/your-username/xauusd-trading-journal
- **文档**: https://docs.xauusd-trading-journal.com

---

**安装完成后，建议先阅读用户手册了解基本功能，然后开始记录您的交易数据！**

**最后更新**: 2025年8月4日
