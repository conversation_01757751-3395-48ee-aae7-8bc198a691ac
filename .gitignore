# macOS系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux系统文件
*~
.fuse_hidden*
.directory
.Trash-*

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 日志文件
*.log
logs/
*.log.*

# 编译文件和缓存
*.o
*.obj
*.exe
*.dll
*.so
*.dylib
*.class
*.pyc
*.pyo
__pycache__/
*.cache

# IDE和编辑器文件
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.atom/
*.code-workspace

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# 环境变量和配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config.local.*

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 压缩文件（通常不需要版本控制）
*.zip
*.rar
*.7z
*.tar.gz
*.tgz

# 个人笔记和草稿
notes.txt
draft.*
TODO.txt
