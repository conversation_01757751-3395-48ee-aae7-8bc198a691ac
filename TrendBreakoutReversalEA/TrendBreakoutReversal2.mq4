//+------------------------------------------------------------------+
//|                          TrendBreakoutReversal.mq4              |
//|                          趋势突破与反转确认做多策略              |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025"
#property version   "1.00"
#property strict

// ================================
// 输入参数
// ================================
input int      EMA20_Period = 20;           // EMA20周期
input int      EMA50_Period = 50;           // EMA50周期
input int      RSI_Period = 14;             // RSI周期
input int      Vol_MA_Period = 20;          // 成交量均线周期
input double   RiskPercent = 2.0;           // 风险比例(%)
input int      ATR_Period = 14;             // ATR周期
input double   ProfitRatio = 2.0;           // 盈亏比
input int      MagicNumber = 123456;        // 魔术号

// ================================
// 全局变量
// ================================
// 3分钟K线数据结构
struct Bar3M {
   datetime time;
   double open;
   double high;
   double low;
   double close;
   double volume;
};

Bar3M bars3M[];              // 3分钟K线数组
int bars3M_count = 0;        // 3分钟K线数量
datetime last_bar_time = 0;  // 上一个1分钟K线时间

// 指标缓存
double ema20[], ema50[];
double rsi_values[];
double atr_values[];
double vol_ma[];
double macd_main[], macd_signal[], macd_hist[];

// 策略状态监控
struct StrategyMonitor {
   // 突破策略监控
   bool breakout_uptrend;
   bool breakout_signal;
   double breakout_stop;
   double breakout_target;
   double breakout_pos_size;
   
   // 反转策略监控
   bool reversal_oversold;
   bool reversal_hammer;
   bool reversal_divergence;
   bool reversal_near_support;
   bool reversal_signal;
   double reversal_stop;
   double reversal_target;
   double reversal_pos_size;
   
   // 仓位信息
   double total_position;
   double next_risk_amount;
   double account_equity;
   int open_orders;
};

StrategyMonitor monitor;

// 仪表盘设置
int panel_x = 10;
int panel_y = 30;
int panel_width = 600;
int column_width = 200;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
   // 初始化数组
   ArrayResize(bars3M, 5000);
   ArraySetAsSeries(bars3M, true);
   
   ArrayResize(ema20, 5000);
   ArrayResize(ema50, 5000);
   ArrayResize(rsi_values, 5000);
   ArrayResize(atr_values, 5000);
   ArrayResize(vol_ma, 5000);
   ArrayResize(macd_main, 5000);
   ArrayResize(macd_signal, 5000);
   ArrayResize(macd_hist, 5000);
   
   ArraySetAsSeries(ema20, true);
   ArraySetAsSeries(ema50, true);
   ArraySetAsSeries(rsi_values, true);
   ArraySetAsSeries(atr_values, true);
   ArraySetAsSeries(vol_ma, true);
   ArraySetAsSeries(macd_main, true);
   ArraySetAsSeries(macd_signal, true);
   ArraySetAsSeries(macd_hist, true);
   
   // 初始化监控结构
   ZeroMemory(monitor);
   
   // 构建历史3分钟K线
   Build3MBars();
   
   // 计算所有指标
   CalculateAllIndicators();
   
   // 创建仪表盘
   CreateDashboard();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   // 删除仪表盘对象
   ObjectsDeleteAll(0, "Dashboard_");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
   // 更新3分钟K线
   if(Update3MBars()) {
      // 有新的3分钟K线形成，重新计算指标
      CalculateAllIndicators();
   }
   
   // 检查交易信号
   CheckTradingSignals();
   
   // 更新仪表盘
   UpdateDashboard();
}

//+------------------------------------------------------------------+
//| 构建3分钟K线                                                     |
//+------------------------------------------------------------------+
void Build3MBars() {
   bars3M_count = 0;
   datetime current_3m_time = 0;
   
   int total_bars = iBars(Symbol(), PERIOD_M1);
   if(total_bars <= 0) return;
   
   for(int i = total_bars - 1; i >= 0; i--) {
      datetime bar_time = iTime(Symbol(), PERIOD_M1, i);
      if(bar_time == 0) continue;
      
      int minutes = TimeMinute(bar_time);
      datetime bar_3m_time = bar_time - (minutes % 3) * 60;
      
      if(bar_3m_time != current_3m_time) {
         // 新的3分钟K线
         if(bars3M_count >= ArraySize(bars3M)) {
            ArrayResize(bars3M, bars3M_count + 100);
         }
         
         bars3M[bars3M_count].time = bar_3m_time;
         bars3M[bars3M_count].open = iOpen(Symbol(), PERIOD_M1, i);
         bars3M[bars3M_count].high = iHigh(Symbol(), PERIOD_M1, i);
         bars3M[bars3M_count].low = iLow(Symbol(), PERIOD_M1, i);
         bars3M[bars3M_count].close = iClose(Symbol(), PERIOD_M1, i);
         bars3M[bars3M_count].volume = (double)iVolume(Symbol(), PERIOD_M1, i);
         
         current_3m_time = bar_3m_time;
         bars3M_count++;
      } else {
         // 更新当前3分钟K线
         if(bars3M_count > 0) {
            int idx = bars3M_count - 1;
            bars3M[idx].high = MathMax(bars3M[idx].high, iHigh(Symbol(), PERIOD_M1, i));
            bars3M[idx].low = MathMin(bars3M[idx].low, iLow(Symbol(), PERIOD_M1, i));
            bars3M[idx].close = iClose(Symbol(), PERIOD_M1, i);
            bars3M[idx].volume += (double)iVolume(Symbol(), PERIOD_M1, i);
         }
      }
   }
   
   if(iBars(Symbol(), PERIOD_M1) > 0) {
      last_bar_time = iTime(Symbol(), PERIOD_M1, 0);
   }
}

//+------------------------------------------------------------------+
//| 更新3分钟K线                                                     |
//+------------------------------------------------------------------+
bool Update3MBars() {
   if(iBars(Symbol(), PERIOD_M1) <= 0) return false;
   
   datetime current_time = iTime(Symbol(), PERIOD_M1, 0);
   if(current_time == 0) return false;
   
   if(current_time == last_bar_time) {
      // 更新当前3分钟K线
      if(bars3M_count > 0) {
         bars3M[0].high = MathMax(bars3M[0].high, iHigh(Symbol(), PERIOD_M1, 0));
         bars3M[0].low = MathMin(bars3M[0].low, iLow(Symbol(), PERIOD_M1, 0));
         bars3M[0].close = iClose(Symbol(), PERIOD_M1, 0);
      }
      return false;
   }
   
   last_bar_time = current_time;
   int minutes = TimeMinute(current_time);
   
   if(minutes % 3 == 0) {
      // 新的3分钟K线开始
      // 将数组向后移动
      for(int i = MathMin(bars3M_count - 1, ArraySize(bars3M) - 2); i > 0; i--) {
         bars3M[i] = bars3M[i-1];
      }
      
      bars3M[0].time = current_time;
      bars3M[0].open = iOpen(Symbol(), PERIOD_M1, 0);
      bars3M[0].high = iHigh(Symbol(), PERIOD_M1, 0);
      bars3M[0].low = iLow(Symbol(), PERIOD_M1, 0);
      bars3M[0].close = iClose(Symbol(), PERIOD_M1, 0);
      bars3M[0].volume = (double)iVolume(Symbol(), PERIOD_M1, 0);
      
      if(bars3M_count < ArraySize(bars3M)) bars3M_count++;
      
      return true;
   } else {
      // 更新当前3分钟K线
      if(bars3M_count > 0) {
         bars3M[0].high = MathMax(bars3M[0].high, iHigh(Symbol(), PERIOD_M1, 0));
         bars3M[0].low = MathMin(bars3M[0].low, iLow(Symbol(), PERIOD_M1, 0));
         bars3M[0].close = iClose(Symbol(), PERIOD_M1, 0);
         bars3M[0].volume += (double)iVolume(Symbol(), PERIOD_M1, 0);
      }
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| 计算所有指标                                                     |
//+------------------------------------------------------------------+
void CalculateAllIndicators() {
   if(bars3M_count < 100) return; // 数据不足
   
   // 计算EMA
   CalculateEMA(ema20, EMA20_Period);
   CalculateEMA(ema50, EMA50_Period);
   
   // 计算RSI
   CalculateRSI();
   
   // 计算ATR
   CalculateATR();
   
   // 计算成交量均线
   CalculateVolumeMA();
   
   // 计算MACD
   CalculateMACD();
}

//+------------------------------------------------------------------+
//| 计算EMA                                                          |
//+------------------------------------------------------------------+
void CalculateEMA(double &ema[], int period) {
   if(bars3M_count < period) return;
   
   int ema_size = ArraySize(ema);
   double multiplier = 2.0 / (period + 1);
   
   // 初始值使用SMA
   double sum = 0;
   int start_idx = MathMin(bars3M_count - 1, ema_size - 1);
   int count = 0;
   
   for(int i = start_idx; i >= 0 && count < period; i--) {
      if(i < bars3M_count) {
         sum += bars3M[i].close;
         count++;
      }
   }
   
   if(count > 0 && start_idx < ema_size) {
      ema[start_idx] = sum / count;
   }
   
   // 计算EMA
   for(int i = start_idx - 1; i >= 0; i--) {
      if(i < ema_size && i < bars3M_count && (i + 1) < ema_size) {
         ema[i] = (bars3M[i].close - ema[i+1]) * multiplier + ema[i+1];
      }
   }
}

//+------------------------------------------------------------------+
//| 计算RSI                                                          |
//+------------------------------------------------------------------+
void CalculateRSI() {
   if(bars3M_count < RSI_Period + 1) return;
   
   int rsi_size = ArraySize(rsi_values);
   double gain_sum = 0, loss_sum = 0;
   
   // 初始平均值
   int start_idx = MathMin(bars3M_count - RSI_Period - 1, rsi_size - 1);
   
   for(int i = start_idx; i < MathMin(start_idx + RSI_Period, bars3M_count - 1); i++) {
      if(i >= 0 && (i + 1) < bars3M_count) {
         double change = bars3M[i].close - bars3M[i+1].close;
         if(change > 0) gain_sum += change;
         else loss_sum += MathAbs(change);
      }
   }
   
   double avg_gain = gain_sum / RSI_Period;
   double avg_loss = loss_sum / RSI_Period;
   
   // 计算RSI
   for(int i = start_idx; i >= 0; i--) {
      if(i < rsi_size && i < bars3M_count - 1) {
         double change = bars3M[i].close - bars3M[i+1].close;
         double gain = change > 0 ? change : 0;
         double loss = change < 0 ? MathAbs(change) : 0;
         
         avg_gain = (avg_gain * (RSI_Period - 1) + gain) / RSI_Period;
         avg_loss = (avg_loss * (RSI_Period - 1) + loss) / RSI_Period;
         
         if(avg_loss == 0) rsi_values[i] = 100;
         else {
            double rs = avg_gain / avg_loss;
            rsi_values[i] = 100 - (100 / (1 + rs));
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 计算ATR                                                          |
//+------------------------------------------------------------------+
void CalculateATR() {
   if(bars3M_count < ATR_Period) return;
   
   int atr_size = ArraySize(atr_values);
   double tr_sum = 0;
   
   // 初始ATR
   int start_idx = MathMin(bars3M_count - ATR_Period, atr_size - 1);
   
   for(int i = start_idx; i < MathMin(start_idx + ATR_Period, bars3M_count); i++) {
      if(i >= 0 && i < bars3M_count) {
         double tr = bars3M[i].high - bars3M[i].low;
         if(i < bars3M_count - 1) {
            tr = MathMax(tr, MathAbs(bars3M[i].high - bars3M[i+1].close));
            tr = MathMax(tr, MathAbs(bars3M[i].low - bars3M[i+1].close));
         }
         tr_sum += tr;
      }
   }
   
   if(start_idx >= 0 && start_idx < atr_size) {
      atr_values[start_idx] = tr_sum / ATR_Period;
   }
   
   // 计算ATR
   for(int i = start_idx - 1; i >= 0; i--) {
      if(i < atr_size && i < bars3M_count && (i + 1) < atr_size) {
         double tr = bars3M[i].high - bars3M[i].low;
         if(i < bars3M_count - 1) {
            tr = MathMax(tr, MathAbs(bars3M[i].high - bars3M[i+1].close));
            tr = MathMax(tr, MathAbs(bars3M[i].low - bars3M[i+1].close));
         }
         
         atr_values[i] = (atr_values[i+1] * (ATR_Period - 1) + tr) / ATR_Period;
      }
   }
}

//+------------------------------------------------------------------+
//| 计算成交量均线                                                   |
//+------------------------------------------------------------------+
void CalculateVolumeMA() {
   int vol_size = ArraySize(vol_ma);
   
   for(int i = 0; i < MathMin(bars3M_count, vol_size); i++) {
      double sum = 0;
      int count = 0;
      
      for(int j = i; j < MathMin(i + Vol_MA_Period, bars3M_count); j++) {
         sum += bars3M[j].volume;
         count++;
      }
      
      if(count > 0) vol_ma[i] = sum / count;
   }
}

//+------------------------------------------------------------------+
//| 计算MACD                                                         |
//+------------------------------------------------------------------+
void CalculateMACD() {
   if(bars3M_count < 26) return;
   
   double ema12[5000], ema26[5000];
   ArraySetAsSeries(ema12, true);
   ArraySetAsSeries(ema26, true);
   ArrayResize(ema12, ArraySize(macd_main));
   ArrayResize(ema26, ArraySize(macd_main));
   
   // 计算12和26周期EMA
   CalculateEMA(ema12, 12);
   CalculateEMA(ema26, 26);
   
   // 计算MACD主线
   for(int i = 0; i < MathMin(bars3M_count, ArraySize(macd_main)); i++) {
      if(i < ArraySize(ema12) && i < ArraySize(ema26)) {
         macd_main[i] = ema12[i] - ema26[i];
      }
   }
   
   // 计算信号线（9周期EMA）
   if(bars3M_count >= 9 && ArraySize(macd_signal) > 8) {
      double multiplier = 2.0 / 10;
      double sum = 0;
      int count = 0;
      
      for(int i = 0; i < 9 && i < ArraySize(macd_main); i++) {
         sum += macd_main[i];
         count++;
      }
      
      if(count > 0 && 8 < ArraySize(macd_signal)) {
         macd_signal[8] = sum / count;
      }
      
      for(int i = 7; i >= 0; i--) {
         if(i < ArraySize(macd_signal) && i < ArraySize(macd_main) && (i + 1) < ArraySize(macd_signal)) {
            macd_signal[i] = (macd_main[i] - macd_signal[i+1]) * multiplier + macd_signal[i+1];
         }
      }
   }
   
   // 计算柱状图
   for(int i = 0; i < MathMin(bars3M_count, ArraySize(macd_hist)); i++) {
      if(i < ArraySize(macd_main) && i < ArraySize(macd_signal)) {
         macd_hist[i] = macd_main[i] - macd_signal[i];
      }
   }
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                     |
//+------------------------------------------------------------------+
void CheckTradingSignals() {
   if(bars3M_count < 100) return; // 数据不足
   
   // 获取当前价格和账户信息
   double current_price = bars3M[0].close;
   monitor.account_equity = AccountEquity();
   
   // ================================
   // 趋势判断
   // ================================
   monitor.breakout_uptrend = false;
   if(ArraySize(ema20) > 1 && ArraySize(ema50) > 1) {
      double ema20_slope = ema20[0] - ema20[1];
      double ema50_slope = ema50[0] - ema50[1];
      monitor.breakout_uptrend = (ema20[0] > ema50[0] && ema20_slope > 0 && ema50_slope > 0);
   }
   
   // ================================
   // 突破信号检测
   // ================================
   double highest_high = GetHighest(1, 20);
   bool breakout = false;
   if(ArraySize(vol_ma) > 0 && vol_ma[0] > 0) {
      breakout = (current_price > highest_high && bars3M[0].volume > vol_ma[0]);
   }
   monitor.breakout_signal = monitor.breakout_uptrend && breakout;
   
   if(monitor.breakout_signal) {
      monitor.breakout_stop = GetLowest(1, 5);
      monitor.breakout_target = current_price + (current_price - monitor.breakout_stop) * ProfitRatio;
      monitor.breakout_pos_size = GetPositionSize(monitor.breakout_stop);
      
      // 检查是否已有相同类型订单
      if(!HasOrderWithComment("Breakout_Long")) {
         OpenOrder(OP_BUY, monitor.breakout_pos_size, monitor.breakout_stop, 
                  monitor.breakout_target, "Breakout_Long");
      }
   }
   
   // ================================
   // 反转信号检测
   // ================================
   monitor.reversal_oversold = false;
   if(ArraySize(rsi_values) > 0) {
      monitor.reversal_oversold = (rsi_values[0] < 30);
   }
   
   // 锤子线判断
   double body = MathAbs(bars3M[0].open - bars3M[0].close);
   double range = bars3M[0].high - bars3M[0].low;
   double lower_shadow = MathMin(bars3M[0].open, bars3M[0].close) - bars3M[0].low;
   double upper_shadow = bars3M[0].high - MathMax(bars3M[0].open, bars3M[0].close);
   
   monitor.reversal_hammer = false;
   if(range > 0) {
      monitor.reversal_hammer = (range > 3 * body && 
                                 lower_shadow > 0.6 * range && 
                                 upper_shadow < 0.3 * range);
   }
   
   // RSI背离
   monitor.reversal_divergence = false;
   if(ArraySize(rsi_values) > 0) {
      double price_lowest = GetLowest(1, 5);
      double rsi_lowest = GetRSILowest(1, 5);
      monitor.reversal_divergence = (bars3M[0].low < price_lowest && rsi_values[0] > rsi_lowest);
   }
   
   // 均线支撑
   monitor.reversal_near_support = false;
   if(ArraySize(ema50) > 0 && ArraySize(atr_values) > 0) {
      monitor.reversal_near_support = (MathAbs(current_price - ema50[0]) < atr_values[0]);
   }
   
   // 反转确认
   monitor.reversal_signal = monitor.reversal_oversold && 
                            ((monitor.reversal_hammer && monitor.reversal_divergence) ||
                             (monitor.reversal_hammer && monitor.reversal_near_support) ||
                             (monitor.reversal_divergence && monitor.reversal_near_support));
   
   if(monitor.reversal_signal && !monitor.breakout_uptrend) {
      if(ArraySize(atr_values) > 0) {
         monitor.reversal_stop = bars3M[0].low - atr_values[0] * 0.5;
         monitor.reversal_target = current_price + (current_price - monitor.reversal_stop) * ProfitRatio;
         monitor.reversal_pos_size = GetPositionSize(monitor.reversal_stop);
         
         // 检查是否已有相同类型订单
         if(!HasOrderWithComment("Reversal_Long")) {
            OpenOrder(OP_BUY, monitor.reversal_pos_size, monitor.reversal_stop, 
                     monitor.reversal_target, "Reversal_Long");
         }
      }
   }
   
   // 更新仓位信息
   UpdatePositionInfo();
}

//+------------------------------------------------------------------+
//| 获取最高价                                                       |
//+------------------------------------------------------------------+
double GetHighest(int start, int count) {
   double highest = 0;
   for(int i = start; i < MathMin(start + count, bars3M_count); i++) {
      if(i >= 0 && i < bars3M_count && bars3M[i].high > highest) {
         highest = bars3M[i].high;
      }
   }
   return highest;
}

//+------------------------------------------------------------------+
//| 获取最低价                                                       |
//+------------------------------------------------------------------+
double GetLowest(int start, int count) {
   double lowest = 999999;
   for(int i = start; i < MathMin(start + count, bars3M_count); i++) {
      if(i >= 0 && i < bars3M_count && bars3M[i].low < lowest) {
         lowest = bars3M[i].low;
      }
   }
   return lowest;
}

//+------------------------------------------------------------------+
//| 获取RSI最低值                                                    |
//+------------------------------------------------------------------+
double GetRSILowest(int start, int count) {
   double lowest = 100;
   int rsi_size = ArraySize(rsi_values);
   
   for(int i = start; i < MathMin(start + count, rsi_size); i++) {
      if(i >= 0 && i < rsi_size && rsi_values[i] < lowest) {
         lowest = rsi_values[i];
      }
   }
   return lowest;
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                     |
//+------------------------------------------------------------------+
double GetPositionSize(double stop_price) {
   if(bars3M_count <= 0) return 0.01;
   
   double risk_amount = AccountEquity() * RiskPercent / 100;
   double stop_distance = MathAbs(bars3M[0].close - stop_price);
   
   if(stop_distance <= 0) return 0.01;
   
   double pip_value = MarketInfo(Symbol(), MODE_TICKVALUE);
   if(pip_value <= 0) pip_value = 1;
   
   double point_value = MarketInfo(Symbol(), MODE_POINT);
   if(point_value <= 0) point_value = 0.00001;
   
   double position_size = risk_amount / (stop_distance / point_value * pip_value);
   
   // 调整为符合最小和最大手数要求
   double min_lot = MarketInfo(Symbol(), MODE_MINLOT);
   double max_lot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lot_step = MarketInfo(Symbol(), MODE_LOTSTEP);
   
   if(min_lot <= 0) min_lot = 0.01;
   if(max_lot <= 0) max_lot = 100;
   if(lot_step <= 0) lot_step = 0.01;
   
   position_size = MathFloor(position_size / lot_step) * lot_step;
   position_size = MathMax(min_lot, MathMin(max_lot, position_size));
   
   // 更新下一单风险金额
   monitor.next_risk_amount = risk_amount;
   
   return position_size;
}

//+------------------------------------------------------------------+
//| 检查是否有指定注释的订单                                         |
//+------------------------------------------------------------------+
bool HasOrderWithComment(string comment) {
   for(int i = 0; i < OrdersTotal(); i++) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber &&
            StringFind(OrderComment(), comment) >= 0) {
            return true;
         }
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| 开仓                                                             |
//+------------------------------------------------------------------+
void OpenOrder(int type, double lots, double stop_loss, double take_profit, string comment) {
   double price = (type == OP_BUY) ? Ask : Bid;
   
   // 规范化价格
   int digits = (int)MarketInfo(Symbol(), MODE_DIGITS);
   price = NormalizeDouble(price, digits);
   stop_loss = NormalizeDouble(stop_loss, digits);
   take_profit = NormalizeDouble(take_profit, digits);
   
   int ticket = OrderSend(Symbol(), type, lots, price, 3, stop_loss, take_profit, 
                         comment, MagicNumber, 0, clrGreen);
   
   if(ticket < 0) {
      Print("订单开仓失败: ", GetLastError());
   } else {
      Print("订单开仓成功，订单号: ", ticket);
   }
}

//+------------------------------------------------------------------+
//| 更新仓位信息                                                     |
//+------------------------------------------------------------------+
void UpdatePositionInfo() {
   monitor.total_position = 0;
   monitor.open_orders = 0;
   
   for(int i = 0; i < OrdersTotal(); i++) {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber) {
            monitor.total_position += OrderLots();
            monitor.open_orders++;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 创建仪表盘                                                       |
//+------------------------------------------------------------------+
void CreateDashboard() {
   // 创建背景
   ObjectCreate(0, "Dashboard_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "Dashboard_BG", OBJPROP_XDISTANCE, panel_x);
   ObjectSetInteger(0, "Dashboard_BG", OBJPROP_YDISTANCE, panel_y);
      ObjectSetInteger(0, "Dashboard_BG", OBJPROP_XSIZE, panel_width);
   ObjectSetInteger(0, "Dashboard_BG", OBJPROP_YSIZE, 400);
   ObjectSetInteger(0, "Dashboard_BG", OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(0, "Dashboard_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, "Dashboard_BG", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   
   // 创建标题
   CreateLabel("Dashboard_Title", panel_x + panel_width/2, panel_y + 10, 
              "策略监控仪表盘", clrWhite, 12, true);
   
   // 创建列标题
   CreateLabel("Dashboard_Col1_Title", panel_x + column_width/2, panel_y + 40,
              "突破策略监控", clrYellow, 10, true);
   CreateLabel("Dashboard_Col2_Title", panel_x + column_width + column_width/2, panel_y + 40,
              "反转策略监控", clrYellow, 10, true);
   CreateLabel("Dashboard_Col3_Title", panel_x + column_width*2 + column_width/2, panel_y + 40,
              "仓位信息", clrYellow, 10, true);
}

//+------------------------------------------------------------------+
//| 创建标签                                                         |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, color clr, int size, bool center = false) {
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, size);
   if(center) ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_CENTER);
}

//+------------------------------------------------------------------+
//| 更新仪表盘                                                       |
//+------------------------------------------------------------------+
void UpdateDashboard() {
   int y_offset = 70;
   int line_height = 20;
   
   // 更新突破策略监控
   UpdateLabel("Dashboard_Breakout_1", panel_x + 10, panel_y + y_offset,
              "上升趋势: " + (monitor.breakout_uptrend ? "是" : "否"),
              monitor.breakout_uptrend ? clrGreen : clrRed);
   
   UpdateLabel("Dashboard_Breakout_2", panel_x + 10, panel_y + y_offset + line_height,
              "突破信号: " + (monitor.breakout_signal ? "触发" : "未触发"),
              monitor.breakout_signal ? clrGreen : clrGray);
   
   UpdateLabel("Dashboard_Breakout_3", panel_x + 10, panel_y + y_offset + line_height*2,
              "止损价: " + DoubleToString(monitor.breakout_stop, 2), clrWhite);
   
   UpdateLabel("Dashboard_Breakout_4", panel_x + 10, panel_y + y_offset + line_height*3,
              "目标价: " + DoubleToString(monitor.breakout_target, 2), clrWhite);
   
   UpdateLabel("Dashboard_Breakout_5", panel_x + 10, panel_y + y_offset + line_height*4,
              "仓位大小: " + DoubleToString(monitor.breakout_pos_size, 2), clrWhite);
   
   // 更新反转策略监控
   UpdateLabel("Dashboard_Reversal_1", panel_x + column_width + 10, panel_y + y_offset,
              "RSI超卖: " + (monitor.reversal_oversold ? "是" : "否"),
              monitor.reversal_oversold ? clrGreen : clrGray);
   
   UpdateLabel("Dashboard_Reversal_2", panel_x + column_width + 10, panel_y + y_offset + line_height,
              "锤子线: " + (monitor.reversal_hammer ? "是" : "否"),
              monitor.reversal_hammer ? clrGreen : clrGray);
   
   UpdateLabel("Dashboard_Reversal_3", panel_x + column_width + 10, panel_y + y_offset + line_height*2,
              "RSI背离: " + (monitor.reversal_divergence ? "是" : "否"),
              monitor.reversal_divergence ? clrGreen : clrGray);
   
   UpdateLabel("Dashboard_Reversal_4", panel_x + column_width + 10, panel_y + y_offset + line_height*3,
              "均线支撑: " + (monitor.reversal_near_support ? "是" : "否"),
              monitor.reversal_near_support ? clrGreen : clrGray);
   
   UpdateLabel("Dashboard_Reversal_5", panel_x + column_width + 10, panel_y + y_offset + line_height*4,
              "反转信号: " + (monitor.reversal_signal ? "触发" : "未触发"),
              monitor.reversal_signal ? clrGreen : clrGray);
   
   UpdateLabel("Dashboard_Reversal_6", panel_x + column_width + 10, panel_y + y_offset + line_height*5,
              "止损价: " + DoubleToString(monitor.reversal_stop, 2), clrWhite);
   
   UpdateLabel("Dashboard_Reversal_7", panel_x + column_width + 10, panel_y + y_offset + line_height*6,
              "目标价: " + DoubleToString(monitor.reversal_target, 2), clrWhite);
   
   // 更新仓位信息
   UpdateLabel("Dashboard_Position_1", panel_x + column_width*2 + 10, panel_y + y_offset,
              "账户净值: $" + DoubleToString(monitor.account_equity, 2), clrWhite);
   
   UpdateLabel("Dashboard_Position_2", panel_x + column_width*2 + 10, panel_y + y_offset + line_height,
              "总仓位: " + DoubleToString(monitor.total_position, 2) + " 手", clrWhite);
   
   UpdateLabel("Dashboard_Position_3", panel_x + column_width*2 + 10, panel_y + y_offset + line_height*2,
              "持仓订单: " + IntegerToString(monitor.open_orders), clrWhite);
   
   UpdateLabel("Dashboard_Position_4", panel_x + column_width*2 + 10, panel_y + y_offset + line_height*3,
              "下单风险金额: $" + DoubleToString(monitor.next_risk_amount, 2), clrYellow);
   
   UpdateLabel("Dashboard_Position_5", panel_x + column_width*2 + 10, panel_y + y_offset + line_height*4,
              "风险比例: " + DoubleToString(RiskPercent, 1) + "%", clrWhite);
   
   // 显示当前指标值
   string ema20_str = "N/A";
   string ema50_str = "N/A";
   string rsi_str = "N/A";
   string atr_str = "N/A";
   
   if(ArraySize(ema20) > 0) ema20_str = DoubleToString(ema20[0], 2);
   if(ArraySize(ema50) > 0) ema50_str = DoubleToString(ema50[0], 2);
   if(ArraySize(rsi_values) > 0) rsi_str = DoubleToString(rsi_values[0], 2);
   if(ArraySize(atr_values) > 0) atr_str = DoubleToString(atr_values[0], 2);
   
   UpdateLabel("Dashboard_Indicators_1", panel_x + column_width*2 + 10, panel_y + y_offset + line_height*6,
              "EMA20: " + ema20_str, clrBlue);
   
   UpdateLabel("Dashboard_Indicators_2", panel_x + column_width*2 + 10, panel_y + y_offset + line_height*7,
              "EMA50: " + ema50_str, clrRed);
   
   UpdateLabel("Dashboard_Indicators_3", panel_x + column_width*2 + 10, panel_y + y_offset + line_height*8,
              "RSI: " + rsi_str, clrWhite);
   
   UpdateLabel("Dashboard_Indicators_4", panel_x + column_width*2 + 10, panel_y + y_offset + line_height*9,
              "ATR: " + atr_str, clrWhite);
   
   // 更新3分钟K线状态
   string bar3m_status = "N/A";
   if(bars3M_count > 0) {
      bar3m_status = "当前: " + DoubleToString(bars3M[0].close, 2) + 
                    " H: " + DoubleToString(bars3M[0].high, 2) + 
                    " L: " + DoubleToString(bars3M[0].low, 2);
   }
   
   UpdateLabel("Dashboard_Bar3M_Status", panel_x + 10, panel_y + y_offset + line_height*10,
              "3分钟K线: " + bar3m_status, clrWhite);
   
   // 显示K线数量
   UpdateLabel("Dashboard_Bar_Count", panel_x + 10, panel_y + y_offset + line_height*11,
                            "K线数量: " + IntegerToString(bars3M_count), clrWhite);
}

//+------------------------------------------------------------------+
//| 更新标签内容                                                     |
//+------------------------------------------------------------------+
void UpdateLabel(string name, int x, int y, string text, color clr) {
   if(ObjectFind(0, name) < 0) {
      CreateLabel(name, x, y, text, clr, 9);
   } else {
      ObjectSetString(0, name, OBJPROP_TEXT, text);
      ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   }
}


