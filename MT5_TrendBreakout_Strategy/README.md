# MT5 趋势突破增强策略 EA

一个功能完整的MT5自动交易专家顾问，专为XAUUSD设计，集成趋势突破策略、K线颜色过滤和独立策略开关控制。

## 🎯 策略概述

该EA采用增强的趋势突破策略，支持多空双向交易：

### 📈 多头突破策略
- **条件**：上升趋势 + 价格突破前高 + 成交量放大 + 可选K线颜色过滤
- **入场**：价格突破20根K线最高点
- **确认**：EMA20 > EMA50 且均线向上倾斜
- **过滤**：当前K线为阳线（如启用K线颜色过滤）

### 📉 空头突破策略
- **条件**：下降趋势 + 价格跌破前低 + 成交量放大 + 可选K线颜色过滤
- **入场**：价格跌破20根K线最低点
- **确认**：EMA20 < EMA50 且均线向下倾斜
- **过滤**：当前K线为阴线（如启用K线颜色过滤）

## 🆕 最新功能特性

### ✨ K线颜色过滤功能
- **智能方向过滤**：多头策略要求指定时间框架的当前K线为阳线
- **精确空头控制**：空头策略要求指定时间框架的当前K线为阴线
- **实时监控**：时刻监控当前K线颜色状态
- **灵活时间框架**：支持M1到D1的所有时间框架选择

### 🎛️ 独立策略开关
- **分层控制**：总开关 + 多头开关 + 空头开关
- **灵活交易模式**：只做多、只做空、双向交易
- **实时切换**：参数修改后立即生效
- **风险控制**：可根据市场情况快速调整

## ✨ 核心功能

### 🛡️ 风险管理系统
- **动态仓位计算**：基于账户权益和风险比例
- **智能止损止盈**：基于前期极值点 + 可配置盈亏比
- **追踪止损**：可选的动态追踪止损功能
- **保本功能**：价格移动指定距离后自动将止损移至成本线
- **多单管理**：可配置最大多头和空头持仓数量

### 🎛️ 高级过滤系统
- **均线过滤**：可选的高时间框架均线过滤
- **K线颜色过滤**：基于指定时间框架的K线颜色过滤
- **时间过滤**：精确的交易时间控制（星期+时间段）
- **信号冷却**：防止重复开单的时间间隔控制

### 📊 增强监控仪表盘
- **策略开关状态**：实时显示多头/空头策略启用状态
- **K线颜色监控**：显示当前K线颜色和过滤状态
- **突破信号状态**：实时显示多头和空头突破信号
- **仓位信息**：持仓数量、浮动盈亏、风险金额
- **技术指标**：EMA、成交量等关键数据
- **交易统计**：总体统计、多头突破统计、空头突破统计

## 📁 文件说明

### 主要文件
- **`TrendBreakout_Enhanced_EA.mq5`** - 增强版EA文件（包含所有新功能）
- **`TrendBreakoutReversal_EA.mq5`** - 原版EA文件（基础版本）
- **`README.md`** - 项目说明文档

## 🔧 参数配置

### 📈 指标参数
```
EMA20_Period = 20        // EMA20周期
EMA50_Period = 50        // EMA50周期
RSI_Period = 14          // RSI周期
Volume_MA_Period = 20    // 成交量均线周期
ATR_Period = 14          // ATR周期
```

### 🛡️ 风险管理参数
```
Risk_Percent = 2.0              // 风险比例(%)
Profit_Ratio = 2.0              // 盈亏比
Enable_Trailing_Stop = true     // 启用追踪止损
Trailing_Start_Points = 200     // 追踪止损启动点数
Trailing_Stop_Points = 100      // 追踪止损距离点数
Trailing_Step_Points = 50       // 追踪止损步长点数
Enable_Breakeven = true         // 启用保本功能
Breakeven_Trigger_Points = 150  // 保本触发距离点数
Breakeven_Offset_Points = 10    // 保本偏移点数
```

### 🎛️ 交易过滤参数
```
Enable_MA_Filter = true         // 启用均线过滤
MA_Filter_Timeframe = PERIOD_H1 // 均线过滤时间周期
MA_Filter_Period = 20           // 均线过滤周期
MA_Filter_Method = MODE_EMA     // 均线计算方法
```

### 🆕 K线颜色过滤参数
```
Enable_Candle_Color_Filter = false    // 启用K线颜色过滤
Candle_Filter_Timeframe = PERIOD_H4   // K线过滤时间周期
Candle_Filter_Description = "多头需阳线,空头需阴线"  // 功能说明
```

### ⏰ 时间过滤参数
```
Enable_Time_Filter = false      // 启用时间过滤
Trade_Monday = true             // 周一交易
Trade_Tuesday = true            // 周二交易
Trade_Wednesday = true          // 周三交易
Trade_Thursday = true           // 周四交易
Trade_Friday = true             // 周五交易
Start_Time = "00:00"            // 开始交易时间
End_Time = "23:59"              // 结束交易时间
```

### 📊 仓位管理参数
```
Max_Long_Positions = 3          // 最大多头持仓数
Max_Short_Positions = 3         // 最大空头持仓数
Magic_Number = 123456           // 魔术数字
```

### 🆕 策略开关参数
```
Enable_Breakout_Strategy = true        // 启用突破策略（总开关）
Enable_Long_Breakout_Strategy = true   // 启用多头突破策略
Enable_Short_Breakout_Strategy = true  // 启用空头突破策略
```

### 🔧 系统参数
```
Debug_Mode = true               // 调试模式
```

## 🎯 使用场景和配置

### 常用配置模式

#### 1. 双向交易（默认模式）
```
Enable_Breakout_Strategy = true
Enable_Long_Breakout_Strategy = true
Enable_Short_Breakout_Strategy = true
Enable_Candle_Color_Filter = false
```

#### 2. 只做多头策略
```
Enable_Long_Breakout_Strategy = true
Enable_Short_Breakout_Strategy = false
```

#### 3. 只做空头策略
```
Enable_Long_Breakout_Strategy = false
Enable_Short_Breakout_Strategy = true
```

#### 4. 启用K线颜色过滤
```
Enable_Candle_Color_Filter = true
Candle_Filter_Timeframe = PERIOD_H4
```

#### 5. 强过滤模式
```
Enable_MA_Filter = true
Enable_Candle_Color_Filter = true
Candle_Filter_Timeframe = PERIOD_H4
```

### 市场适应策略
- **牛市环境**：只启用多头策略
- **熊市环境**：只启用空头策略
- **震荡市场**：启用K线颜色过滤
- **重要数据前**：暂停所有策略或特定方向

## 📊 增强监控面板说明

### 第一列：多头突破监控
- 上升趋势状态
- 前高价格水平
- 价格突破状态
- 成交量比率
- 多头突破信号
- 突破策略状态
- **🆕 多头策略开关状态**
- **🆕 K线过滤状态**
- **🆕 多头K线颜色状态**

### 第二列：空头突破监控
- 下降趋势状态
- 前低价格水平
- 价格跌破状态
- 成交量比率
- 空头突破信号
- 突破策略状态
- **🆕 空头策略开关状态**
- **🆕 K线过滤状态**
- **🆕 空头K线颜色状态**

### 第三列：仓位信息
- 总仓位数和手数
- 浮动盈亏
- 账户余额和净值
- 下单风险金额
- 策略状态
- 保本功能状态

### 交易统计区域
- **总体统计**：总成交次数、胜率、净利润
- **多头突破统计**：多头策略的详细表现
- **空头突破统计**：空头策略的详细表现

## 🚀 安装和使用

### 系统要求
- MetaTrader 5 平台
- 推荐在 XAUUSD 3分钟图表上使用
- 确保允许自动交易和 DLL 导入

### 安装步骤
1. 将 `TrendBreakout_Enhanced_EA.mq5` 文件复制到 MT5 的 `MQL5/Experts` 目录
2. 在 MT5 中编译 EA 文件
3. 将 EA 拖拽到 XAUUSD 的 3分钟图表上
4. 配置参数并启用自动交易

### 快速开始
1. **新手推荐**：使用默认参数，先启用一个方向测试
2. **进阶用户**：启用K线颜色过滤，选择H4时间框架
3. **专业用户**：根据市场情况灵活调整策略开关

## ⚠️ 风险提示

**重要风险提示**
- 外汇和贵金属交易存在高风险，可能导致资金损失
- 过往表现不代表未来结果
- 请在充分了解策略原理后使用
- 建议先在模拟账户测试
- 请根据自身风险承受能力调整参数
- 新功能建议先在小仓位测试

## 🔧 技术支持

### 常见问题
1. **EA不执行交易**：检查总开关和对应方向的子开关是否启用
2. **K线过滤不生效**：确认Enable_Candle_Color_Filter=true且时间框架设置正确
3. **监控面板不显示**：检查图表权限设置
4. **策略只执行一个方向**：检查对应的策略开关设置

### 调试模式
- 设置 `Debug_Mode = true` 启用详细日志
- 查看 MT5 专家日志获取详细信息
- 观察仪表盘的实时状态显示

## 📝 版本历史

### v2.0 (当前增强版)
- ✨ 新增K线颜色过滤功能
- ✨ 新增独立策略开关控制
- 📈 增强监控面板显示
- 🔧 优化调试信息输出
- 🛡️ 提升风险控制能力

### v1.0 (基础版)
- 实现基础趋势突破策略
- 添加完整的风险管理系统
- 集成实时监控面板
- 支持多重过滤机制
- 完善的交易统计功能

## 📄 许可证

本项目仅供学习和研究使用。使用本 EA 进行实盘交易的风险由用户自行承担。

---

**免责声明**：本 EA 仅供教育和研究目的。任何交易决策应基于您自己的分析和判断。开发者不对使用本 EA 造成的任何损失承担责任。