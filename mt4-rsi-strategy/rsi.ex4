//+------------------------------------------------------------------+
//|                                  RSI_Accumulation_Strategy_EA.mq4 |
//|           Copyright 2024, MetaQuotes Software Corp. (Integrated) |
//|                                             https://www.mql4.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql4.com"
#property version   "2.30" // RSI-based Exit Logic + MA Filter
#property strict

// --- 输入参数 ---
// 指标参数
input int    RSI_Period = 14;                    // RSI周期
input double RSI_Oversold = 30.0;               // RSI超卖线
input double RSI_Overbought = 70.0;             // RSI超买线

// 均线过滤参数
input bool   UseMAFilter = true;                // 启用均线过滤
input int    MA_Period = 20;                    // 均线周期
input int    MA_Method = MODE_SMA;              // 均线类型 (0=SMA, 1=EMA, 2=SMMA, 3=LWMA)
input int    MA_Applied = PRICE_CLOSE;          // 均线应用价格

// 交易参数
input double TriggerThreshold = 50.0;           // 触发阈值（累积值超过此值后归零时开单）
input double LotSize = 0.01;                    // 手数
input int    MagicNumber = 12345;               // 魔术数字
input int    Slippage = 3;                      // 滑点

// 平仓条件参数 (RSI-based)
input double Buy_TakeProfit_RSI = 75.0;         // 多单RSI止盈水平 (RSI > X)
input double Buy_StopLoss_RSI = 25.0;           // 多单RSI止损水平 (RSI < X)
input double Sell_TakeProfit_RSI = 25.0;        // 空单RSI止盈水平 (RSI < X)
input double Sell_StopLoss_RSI = 75.0;          // 空单RSI止损水平 (RSI > X)

// --- 全局变量 ---
double   AccumulationBuffer[]; // 用于存储内置计算的累积值
datetime LastBarTime = 0;
bool     NewBarFlag = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    ArraySetAsSeries(AccumulationBuffer, true);

    if(IsTesting())
    {
        Print("=== 回测模式启动 (集成版 v2.3) ===");
        Print("交易品种: ", Symbol());
        Print("魔术数字: ", MagicNumber);
        Print("RSI平仓逻辑已启用");
        if(UseMAFilter)
        {
            string ma_type_str = "";
            switch(MA_Method)
            {
                case MODE_SMA: ma_type_str = "SMA"; break;
                case MODE_EMA: ma_type_str = "EMA"; break;
                case MODE_SMMA: ma_type_str = "SMMA"; break;
                case MODE_LWMA: ma_type_str = "LWMA"; break;
                default: ma_type_str = "Unknown"; break;
            }
            Print("均线过滤已启用: ", ma_type_str, "(", MA_Period, ")");
        }
        else
        {
            Print("均线过滤已禁用");
        }
    }

    Print("RSI累积强度策略EA (集成版 v2.3) 已启动");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(IsTesting())
    {
        Print("=== 回测结束统计 ===");
        PrintTradeStatistics();
    }
    Print("RSI累积强度策略EA (集成版) 已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(Time[0] != LastBarTime)
    {
        LastBarTime = Time[0];
        NewBarFlag = true;
    }
    else
    {
        NewBarFlag = false;
    }

    if(!NewBarFlag) return;

    if(Bars < RSI_Period + 50) return;

    CalculateInternalAccumulation();
    CheckTradingSignals();
    ManageOrdersByRSI();
}

//+------------------------------------------------------------------+
//| 内置累积强度指标计算逻辑                                            |
//+------------------------------------------------------------------+
void CalculateInternalAccumulation()
{
    int rates_total = Bars;
    if(ArraySize(AccumulationBuffer) < rates_total)
    {
        ArrayResize(AccumulationBuffer, rates_total);
    }
    
    for(int i = rates_total - RSI_Period - 2; i >= 0; i--)
    {
        double rsi = iRSI(NULL, 0, RSI_Period, PRICE_CLOSE, i);
        double prev_accumulation = AccumulationBuffer[i+1];
        
        if(rsi < RSI_Oversold)
        {
            AccumulationBuffer[i] = prev_accumulation + (rsi - RSI_Oversold);
        }
        else if(rsi > RSI_Overbought)
        {
            AccumulationBuffer[i] = prev_accumulation + (rsi - RSI_Overbought);
        }
        else
        {
            AccumulationBuffer[i] = 0.0;
        }
    }
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                      |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    double currentAccumulation = AccumulationBuffer[1];
    double prevAccumulation = AccumulationBuffer[2];

    static datetime lastDebugTime = 0;
    if(Time[1] != lastDebugTime)
    {
        lastDebugTime = Time[1];
        Print("调试信息 (Bar Time: ", TimeToString(Time[1]), ") - ",
              "当前累积值 (Bar[1]): ", DoubleToString(currentAccumulation, 2),
              ", 前一累积值 (Bar[2]): ", DoubleToString(prevAccumulation, 2),
              ", 当前订单数: ", CountOrders());
    }

    if(CountOrders() > 0) return;

    if(MathAbs(prevAccumulation) >= TriggerThreshold && MathAbs(currentAccumulation) < 0.01)
    {
        Print("触发交易信号! 原因: 累积值归零",
              ", 前一累积值: ", DoubleToString(prevAccumulation, 2),
              ", 当前累积值: ", DoubleToString(currentAccumulation, 2));

        // 获取均线过滤条件
        bool maFilterPassed = true;
        if(UseMAFilter)
        {
            double ma_value = iMA(NULL, 0, MA_Period, 0, MA_Method, MA_Applied, 1);
            double current_price = Close[1];

            Print("均线过滤检查 - 当前价格: ", DoubleToString(current_price, Digits),
                  ", 均线值: ", DoubleToString(ma_value, Digits));

            if(prevAccumulation > 0) // 准备开空单
            {
                maFilterPassed = (current_price < ma_value); // 价格在均线下方才能做空
                if(!maFilterPassed)
                {
                    Print("均线过滤阻止开空单: 价格(", DoubleToString(current_price, Digits),
                          ") 不在均线(", DoubleToString(ma_value, Digits), ")下方");
                }
            }
            else if(prevAccumulation < 0) // 准备开多单
            {
                maFilterPassed = (current_price > ma_value); // 价格在均线上方才能做多
                if(!maFilterPassed)
                {
                    Print("均线过滤阻止开多单: 价格(", DoubleToString(current_price, Digits),
                          ") 不在均线(", DoubleToString(ma_value, Digits), ")上方");
                }
            }
        }

        // 只有通过均线过滤才执行交易
        if(maFilterPassed)
        {
            if(prevAccumulation > 0)
            {
                Print("准备开空单 (已通过均线过滤)");
                OpenOrder(OP_SELL);
            }
            else if(prevAccumulation < 0)
            {
                Print("准备开多单 (已通过均线过滤)");
                OpenOrder(OP_BUY);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 开仓函数 (无固定止盈止损)                                           |
//+------------------------------------------------------------------+
void OpenOrder(int orderType)
{
    double price;
    color orderColor;

    if(orderType == OP_BUY)
    {
        price = NormalizeDouble(Ask, Digits);
        orderColor = clrBlue;
    }
    else if(orderType == OP_SELL)
    {
        price = NormalizeDouble(Bid, Digits);
        orderColor = clrRed;
    }
    else
    {
        return; // 无效的订单类型
    }

    // 开仓时不设置止盈止损 (sl=0, tp=0)
    int ticket = OrderSend(Symbol(), orderType, LotSize, price, Slippage, 0, 0,
                          "RSI累积策略(RSI Exit)", MagicNumber, 0, orderColor);

    if(ticket > 0)
    {
        Print("订单已开启: ", (orderType == OP_BUY ? "买入" : "卖出"),
              " 价格: ", DoubleToString(price, Digits));
    }
    else
    {
        Print("开仓失败，错误代码: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 基于RSI管理现有订单 (替换旧的追踪止损)                               |
//+------------------------------------------------------------------+
void ManageOrdersByRSI()
{
    // 获取当前已完成K线的RSI值
    double current_rsi = iRSI(NULL, 0, RSI_Period, PRICE_CLOSE, 1);
    if(current_rsi == EMPTY_VALUE)
    {
        Print("无法计算RSI值，退出订单管理。错误: ", GetLastError());
        return;
    }

    // 倒序遍历所有订单
    for(int i = OrdersTotal() - 1; i >= 0; i--)
    {
        if(OrderSelect(i, SELECT_BY_POS) &&
           OrderMagicNumber() == MagicNumber &&
           OrderSymbol() == Symbol())
        {
            bool shouldClose = false;
            string reason = "";

            // --- 多单平仓逻辑 ---
            if(OrderType() == OP_BUY)
            {
                // 止盈条件
                if(current_rsi > Buy_TakeProfit_RSI)
                {
                    shouldClose = true;
                    reason = "多单RSI止盈";
                }
                // 止损条件
                else if(current_rsi < Buy_StopLoss_RSI)
                {
                    shouldClose = true;
                    reason = "多单RSI止损";
                }
            }
            // --- 空单平仓逻辑 ---
            else if(OrderType() == OP_SELL)
            {
                // 止盈条件
                if(current_rsi < Sell_TakeProfit_RSI)
                {
                    shouldClose = true;
                    reason = "空单RSI止盈";
                }
                // 止损条件
                else if(current_rsi > Sell_StopLoss_RSI)
                {
                    shouldClose = true;
                    reason = "空单RSI止损";
                }
            }

            // 如果满足平仓条件
            if(shouldClose)
            {
                Print("准备平仓: Ticket #", OrderTicket(), " (", reason, "), 当前RSI: ", DoubleToString(current_rsi, 2));
                bool res = OrderClose(OrderTicket(), OrderLots(), OrderClosePrice(), Slippage, clrYellow);
                if(!res)
                {
                    Print("平仓失败, Ticket #", OrderTicket(), ", 错误代码: ", GetLastError());
                }
                // 平仓后最好跳出本次循环的剩余部分，处理下一个订单
                continue; 
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 统计订单数量                                                      |
//+------------------------------------------------------------------+
int CountOrders()
{
    int count = 0;
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS) &&
           OrderMagicNumber() == MagicNumber &&
           OrderSymbol() == Symbol())
        {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| 打印交易统计                                                      |
//+------------------------------------------------------------------+
void PrintTradeStatistics()
{
    int totalTrades = 0;
    int winTrades = 0;
    double totalProfit = 0;

    for(int i = 0; i < OrdersHistoryTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY))
        {
            if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol())
            {
                totalTrades++;
                if(OrderProfit() > 0) winTrades++;
                totalProfit += OrderProfit();
            }
        }
    }

    Print("历史总交易数: ", totalTrades);
    Print("盈利交易数: ", winTrades);
    if(totalTrades > 0)
    {
        Print("胜率: ", DoubleToString(winTrades * 100.0 / totalTrades, 2), "%");
        Print("总盈亏: ", DoubleToString(totalProfit, 2));
    }
    else
    {
        Print("胜率: 0.00%");
        Print("总盈亏: 0.00");
    }
}