//+------------------------------------------------------------------+
//|                                                   Gold Pro EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- Input Parameters
input int      EmaFastLen = 50;           // Fast EMA Length
input int      EmaSlowLen = 200;          // Slow EMA Length
input int      RsiLength = 14;            // RSI Length
input int      AtrLength = 14;            // ATR Length
input double   TrailATRMult = 3.0;        // ATR Trail Multiplier
input double   LotSize = 0.1;             // Custom Lot Size
input int      MagicNumber = 123456;      // Magic Number

//--- Global Variables
CTrade trade;
int emaFastHandle, emaSlowHandle, macdHandle, rsiHandle, atrHandle;
double emaFastBuffer[], emaSlowBuffer[], macdMainBuffer[], macdSignalBuffer[], rsiBuffer[], atrBuffer[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Set magic number
   trade.SetExpertMagicNumber(MagicNumber);

   //--- Create indicators
   emaFastHandle = iMA(_Symbol, PERIOD_CURRENT, EmaFastLen, 0, MODE_EMA, PRICE_CLOSE);
   emaSlowHandle = iMA(_Symbol, PERIOD_CURRENT, EmaSlowLen, 0, MODE_EMA, PRICE_CLOSE);
   macdHandle = iMACD(_Symbol, PERIOD_CURRENT, 12, 26, 9, PRICE_CLOSE);
   rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, RsiLength, PRICE_CLOSE);
   atrHandle = iATR(_Symbol, PERIOD_CURRENT, AtrLength);

   //--- Check if indicators are created successfully
   if(emaFastHandle == INVALID_HANDLE || emaSlowHandle == INVALID_HANDLE ||
      macdHandle == INVALID_HANDLE || rsiHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
   {
      Print("Error creating indicators");
      return INIT_FAILED;
   }

   //--- Set array as series
   ArraySetAsSeries(emaFastBuffer, true);
   ArraySetAsSeries(emaSlowBuffer, true);
   ArraySetAsSeries(macdMainBuffer, true);
   ArraySetAsSeries(macdSignalBuffer, true);
   ArraySetAsSeries(rsiBuffer, true);
   ArraySetAsSeries(atrBuffer, true);

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   IndicatorRelease(emaFastHandle);
   IndicatorRelease(emaSlowHandle);
   IndicatorRelease(macdHandle);
   IndicatorRelease(rsiHandle);
   IndicatorRelease(atrHandle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if new bar
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(currentBarTime == lastBarTime)
      return;
   lastBarTime = currentBarTime;

   //--- Get indicator values
   if(!GetIndicatorValues())
      return;

   //--- Calculate signals
   bool bullishTrend = emaFastBuffer[1] > emaSlowBuffer[1] && iClose(_Symbol, PERIOD_CURRENT, 1) > emaSlowBuffer[1];
   bool bearishTrend = emaFastBuffer[1] < emaSlowBuffer[1] && iClose(_Symbol, PERIOD_CURRENT, 1) < emaSlowBuffer[1];

   //--- MACD crossover signals
   bool macdBullish = macdMainBuffer[1] > macdSignalBuffer[1] && macdMainBuffer[2] <= macdSignalBuffer[2];
   bool macdBearish = macdMainBuffer[1] < macdSignalBuffer[1] && macdMainBuffer[2] >= macdSignalBuffer[2];

   //--- RSI conditions
   bool rsiBullish = rsiBuffer[1] > 50 && rsiBuffer[1] < 70;
   bool rsiBearish = rsiBuffer[1] < 50 && rsiBuffer[1] > 30;

   //--- Entry conditions
   bool longCondition = bullishTrend && macdBullish && rsiBullish;
   bool shortCondition = bearishTrend && macdBearish && rsiBearish;

   //--- Check current positions
   bool hasLongPosition = HasPosition(POSITION_TYPE_BUY);
   bool hasShortPosition = HasPosition(POSITION_TYPE_SELL);

   //--- Execute trades
   if(longCondition && !hasLongPosition)
   {
      if(hasShortPosition)
         CloseAllPositions();

      double sl = iClose(_Symbol, PERIOD_CURRENT, 1) - atrBuffer[1] * TrailATRMult;
      OpenPosition(ORDER_TYPE_BUY, LotSize, sl, 0);
   }
   else if(shortCondition && !hasShortPosition)
   {
      if(hasLongPosition)
         CloseAllPositions();

      double sl = iClose(_Symbol, PERIOD_CURRENT, 1) + atrBuffer[1] * TrailATRMult;
      OpenPosition(ORDER_TYPE_SELL, LotSize, sl, 0);
   }

   //--- Update trailing stops
   UpdateTrailingStops();
}

//+------------------------------------------------------------------+
//| Get indicator values                                             |
//+------------------------------------------------------------------+
bool GetIndicatorValues()
{
   if(CopyBuffer(emaFastHandle, 0, 0, 3, emaFastBuffer) < 3 ||
      CopyBuffer(emaSlowHandle, 0, 0, 3, emaSlowBuffer) < 3 ||
      CopyBuffer(macdHandle, MAIN_LINE, 0, 3, macdMainBuffer) < 3 ||
      CopyBuffer(macdHandle, SIGNAL_LINE, 0, 3, macdSignalBuffer) < 3 ||
      CopyBuffer(rsiHandle, 0, 0, 3, rsiBuffer) < 3 ||
      CopyBuffer(atrHandle, 0, 0, 3, atrBuffer) < 3)
   {
      Print("Error copying indicator buffers");
      return false;
   }
   return true;
}

//+------------------------------------------------------------------+
//| Check if position exists                                         |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_POSITION_TYPE type)
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         if(PositionGetInteger(POSITION_TYPE) == type)
            return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| Open position                                                    |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE type, double lots, double sl, double tp)
{
   double price = (type == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);

   if(type == ORDER_TYPE_BUY)
      trade.Buy(lots, _Symbol, price, sl, tp, "Gold Pro EA Long");
   else
      trade.Sell(lots, _Symbol, price, sl, tp, "Gold Pro EA Short");
}

//+------------------------------------------------------------------+
//| Close all positions                                              |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         trade.PositionClose(PositionGetTicket(i));
      }
   }
}

//+------------------------------------------------------------------+
//| Update trailing stops                                            |
//+------------------------------------------------------------------+
void UpdateTrailingStops()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         ulong ticket = PositionGetTicket(i);
         double currentPrice = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                              SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
         double currentSL = PositionGetDouble(POSITION_SL);
         double newSL;

         if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
         {
            newSL = currentPrice - atrBuffer[1] * TrailATRMult;
            if(newSL > currentSL)
               trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP));
         }
         else
         {
            newSL = currentPrice + atrBuffer[1] * TrailATRMult;
            if(newSL < currentSL || currentSL == 0)
               trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP));
         }
      }
   }
}